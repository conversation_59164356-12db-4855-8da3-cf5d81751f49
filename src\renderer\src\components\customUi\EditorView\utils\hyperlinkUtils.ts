import { Block, BlockNoteEditor } from '@blocknote/core';
import { SimplifiedHyperlinkDictionary } from './../types';

/**
 * Extracts hyperlinks from BlockNote blocks and returns simplified text -> URL mapping
 */
export const extractHyperlinksFromBlocks = (blocks: Block[]): SimplifiedHyperlinkDictionary => {
  console.log('[extractHyperlinksFromBlocks] 🔍 Starting simplified hyperlink extraction from blocks');
  console.log('[extractHyperlinksFromBlocks] 📦 Input blocks:', JSON.stringify(blocks, null, 2));

  const hyperlinkDict: SimplifiedHyperlinkDictionary = {};

  blocks.forEach((block, blockIndex) => {
    console.log(`[extractHyperlinksFromBlocks] 📦 Processing block ${blockIndex}:`, JSON.stringify(block, null, 2));

    if (block.content && Array.isArray(block.content)) {
      block.content.forEach((contentItem: any, itemIndex) => {
        console.log(`[extractHyperlinksFromBlocks] 📄 Processing content item ${itemIndex}:`, JSON.stringify(contentItem, null, 2));

        if (contentItem.type === 'link' && contentItem.href) {
          console.log(`[extractHyperlinksFromBlocks] 🔗 Found link item with href:`, contentItem.href);

          // Extract text from link content
          let linkText = '';
          if (contentItem.content && Array.isArray(contentItem.content)) {
            linkText = contentItem.content
              .filter((item: any) => item.type === 'text')
              .map((item: any) => item.text || '')
              .join('');
            console.log(`[extractHyperlinksFromBlocks] 📝 Extracted link text:`, JSON.stringify(linkText));
          }

          if (linkText) {
            // Normalize the text for dictionary key (strip and clean)
            const normalizedText = linkText
              .replace(/\r\n/g, '\n')
              .replace(/\r/g, '\n')
              .replace(/\n\s*\n/g, '\n')
              .trim();

            hyperlinkDict[normalizedText] = contentItem.href;
            console.log(`[extractHyperlinksFromBlocks] ✅ Added to dictionary:`, {
              normalizedText: JSON.stringify(normalizedText),
              href: contentItem.href
            });
          }
        }
      });
    }
  });

  console.log('[extractHyperlinksFromBlocks] ✅ Final hyperlink dictionary:', JSON.stringify(hyperlinkDict, null, 2));
  return hyperlinkDict;
};

/**
 * Extracts hyperlinks from HTML content and returns simplified text -> URL mapping
 */
export const extractHyperlinksFromHtml = (html: string): SimplifiedHyperlinkDictionary => {
  console.log('[extractHyperlinksFromHtml] 🔍 Starting simplified hyperlink extraction from HTML');
  console.log('[extractHyperlinksFromHtml] 🌐 Input HTML:', html);

  const hyperlinkDict: SimplifiedHyperlinkDictionary = {};
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;

  const links = tempDiv.querySelectorAll('a[href]');
  console.log('[extractHyperlinksFromHtml] 🔗 Found link elements:', links.length);

  links.forEach((link, index) => {
    const linkText = link.textContent || '';
    const href = link.getAttribute('href') || '';

    console.log(`[extractHyperlinksFromHtml] 🔗 Processing link ${index}:`);
    console.log(`  📝 Link text: ${JSON.stringify(linkText)}`);
    console.log(`  🌐 Link href: ${JSON.stringify(href)}`);

    if (linkText && href) {
      // Normalize the text for dictionary key (strip and clean)
      const normalizedText = linkText
        .replace(/\r\n/g, '\n')
        .replace(/\r/g, '\n')
        .replace(/\n\s*\n/g, '\n')
        .trim();

      hyperlinkDict[normalizedText] = href;
      console.log(`  ✅ Added to dictionary:`, {
        normalizedText: JSON.stringify(normalizedText),
        href: href
      });
    } else {
      console.log(`  ⚠️ Skipping link - missing text or href`);
    }
  });

  console.log('[extractHyperlinksFromHtml] ✅ Final hyperlink dictionary:', JSON.stringify(hyperlinkDict, null, 2));
  return hyperlinkDict;
};

/**
 * Normalizes text for consistent dictionary key matching
 */
export const normalizeTextForDictionary = (text: string): string => {
  return text
    .replace(/\r\n/g, '\n')
    .replace(/\r/g, '\n')
    .replace(/\n\s*\n/g, '\n')
    .trim();
};

/**
 * Case-insensitive text matching for hyperlink restoration
 */
export const textContainsCaseInsensitive = (text: string, searchText: string): boolean => {
  return text.toLowerCase().includes(searchText.toLowerCase());
};

/**
 * Splits text into segments, converting the first case-insensitive match to a link
 * Returns array of content items (text and link items)
 */
export const splitTextWithLink = (text: string, dictText: string, url: string): any[] => {
  const lowerText = text.toLowerCase();
  const lowerDictText = dictText.toLowerCase();
  const matchIndex = lowerText.indexOf(lowerDictText);
  
  if (matchIndex === -1) {
    // No match found, return original text
    return [{ type: 'text', text: text, styles: {} }];
  }

  // Extract the actual case-preserved text for the link
  const actualMatchText = text.substring(matchIndex, matchIndex + dictText.length);
  const beforeMatch = text.substring(0, matchIndex);
  const afterMatch = text.substring(matchIndex + dictText.length);

  const segments: any[] = [];

  // Add text before the match (if any)
  if (beforeMatch) {
    segments.push({ type: 'text', text: beforeMatch, styles: {} });
  }

  // Add the link
  segments.push({
    type: 'link',
    href: url,
    content: [{ type: 'text', text: actualMatchText, styles: {} }]
  });

  // Add text after the match (if any)
  if (afterMatch) {
    segments.push({ type: 'text', text: afterMatch, styles: {} });
  }

  return segments;
};

/**
 * Creates linked content from text and hyperlink matches with overlap protection
 * Processes longest matches first to prevent substring conflicts
 */
export const createLinkedContent = (text: string, linkMatches: [string, string][]): any[] => {
  // Sort by text length (longest first) to handle overlapping matches correctly
  const sortedLinkMatches = linkMatches.sort((a, b) => b[0].length - a[0].length);
  
  console.log('[createLinkedContent] Processing matches in order:', sortedLinkMatches.map(([text, url]) => `\"${text}\" (${text.length} chars)`));

  // Start with the original text as a single text content item
  let contentItems: any[] = [{ type: 'text', text: text, styles: {} }];

  // Process each link match, longest first
  sortedLinkMatches.forEach(([dictText, url]) => {
    console.log(`[createLinkedContent] Processing: \"${dictText}\" -> ${url}`);
    
    // Process each content item, looking for text items that contain our match
    const newContentItems: any[] = [];
    
    contentItems.forEach((contentItem) => {
      if (contentItem.type === 'text' && contentItem.text) {
        // Only process text items (not existing links)
        const segments = splitTextWithLink(contentItem.text, dictText, url);
        newContentItems.push(...segments);
      } else {
        // Keep existing links unchanged
        newContentItems.push(contentItem);
      }
    });
    
    contentItems = newContentItems;
    console.log(`[createLinkedContent] After processing \"${dictText}\":`, contentItems.length, 'items');
  });

  return contentItems;
};

/**
 * Enhanced restoreHyperlinksInSpecificBlocks with improved overlap handling
 */
export const restoreHyperlinksInSpecificBlocks = (
  affectedBlocks: Block[],
  pastedText: string,
  hyperlinkDict: SimplifiedHyperlinkDictionary,
  editor: BlockNoteEditor
): void => {
  console.log('[EditorView] 🎯 Restoring hyperlinks in specific blocks only. Affected blocks:', affectedBlocks.length);
  console.log('[EditorView] 📚 Dictionary keys by length:', 
    Object.keys(hyperlinkDict)
      .sort((a, b) => b.length - a.length)
      .map(key => `\"${key}\" (${key.length} chars)`)
  );

  affectedBlocks.forEach((block) => {
    if (block.content && Array.isArray(block.content)) {
      let blockModified = false;
      
      const newContent = block.content.map((contentItem: any) => {
        if (contentItem.type === 'text' && contentItem.text) {
          // Filter dictionary entries that are actually found in this specific contentItem's text
          const matchingEntries = Object.entries(hyperlinkDict).filter(([dictText]) =>
            textContainsCaseInsensitive(contentItem.text, dictText)
          );

          if (matchingEntries.length > 0) {
            console.log('[EditorView] 🎯 Found text matching dictionary keys in block:', block.id);
            console.log('  📝 Text snippet:', JSON.stringify(contentItem.text.substring(0, 100)));
            console.log('  🔗 Matching entries:', matchingEntries.map(([text, url]) => `\"${text}\" -> ${url}`));
            
            blockModified = true;
            return createLinkedContent(contentItem.text, matchingEntries);
          }
        }
        return contentItem;
      }).flat(); // .flat() is important as createLinkedContent returns an array

      if (blockModified) {
        console.log('[EditorView] 🔄 Updating block with new content:', block.id);
        console.log('  📦 New content structure:', newContent.map(item => 
          item.type === 'link' ? `[LINK: \"${item.content[0]?.text}\" -> ${item.href}]` : `[TEXT: \"${item.text?.substring(0, 20)}...\"]`
        ));
        editor.updateBlock(block, { content: newContent });
      }
    }
  });
  
  console.log('[EditorView] ✅ Finished restoring hyperlinks in specific blocks.');
};